import pandas as pd
import numpy as np
from typing import Dict, List, Any, Optional, Tuple
import os
import re
import json
from langchain_community.chat_models import MoonshotChat
from langchain_core.messages import HumanMessage, SystemMessage, BaseMessage, ToolMessage
from langchain_core.messages import AIMessage
from pydantic import BaseModel, Field, ValidationError
from langchain_core.tools import tool
from langgraph.graph import StateGraph, END
from langgraph.prebuilt import ToolNode
from langchain_core.utils.function_calling import convert_to_openai_tool
from langgraph.types import interrupt

from ..state import WorkflowState
from ..database import DatabaseManager
from ..utils import logger

# --- 1. Agent System Prompt (将在下一步骤中更新) ---
AGENT_SYSTEM_PROMPT = """你是一个专家级的薪酬计算智能体，你的核心工作模式是“分析 -> 决策 -> 执行”。

**最终目标**: 准确无误地计算出表格中每位员工的应付薪酬，并将结果存入数据库。

**你的工具箱 (Toolbox)**:
- `get_sheet_preview`: **(分析工具)** 读取表格预览，用于分析。
- `ask_user_for_clarification`: **(分析工具)** 当信息不足时，向用户提问。
- `process_with_multiplication`: **(执行工具)** 处理所有“数量 x 单价”的场景。
- `process_with_fixed_rate`: **(执行工具)** 处理“数量 x 固定单价”的场景。
- `process_from_direct_amount`: **(执行工具)** 处理直接从表格中提取最终金额的场景。

**核心工作流程**:
你的任务是先分析，然后做出最终决策，最后调用**一个**执行工具来完成所有工作。

1.  **分析 (Analyze)**:
    - 你的第一个动作**永远**是调用 `get_sheet_preview` 来理解表格的结构和内容。
    - 分析预览数据，识别出关键列和计算逻辑。
    - 如果预览信息不充分 (例如缺少单价), 你的下一步是调用 `ask_user_for_clarification`。

2.  **决策与执行 (Decide & Execute)**:
    - 当你拥有了所有必要信息（通过预览和向用户提问）后，你必须做出最终决策。
    - **决策**意味着选择一个最合适的 `process_*` 执行工具，并准备好它需要的所有参数。
    - 你**只能调用一次**执行工具。这个调用代表了你分析工作的最终成果。

    **场景示例 A: 表格信息完整 (工时 * 时薪)**
    1.  **分析**: 调用 `get_sheet_preview` -> 发现有'姓名'、'工时'、'时薪'列。
    2.  **决策与执行**: 调用 `process_with_multiplication`，并传入 `project_id` 和 `column_map={'employee_name': '姓名', 'quantity': '工时', 'price': '时薪'}`。任务完成。

    **场景示例 B: 缺少信息 (需要统一单价)**
    1.  **分析**: 调用 `get_sheet_preview` -> 发现只有'姓名'、'工时'列。
    2.  **分析**: 调用 `ask_user_for_clarification`，提问并获得 `fixed_rate=25.0`。
    3.  **决策与执行**: 调用 `process_with_fixed_rate`，并传入 `project_id`、`column_map={'employee_name': '姓名', 'quantity': '工时'}` 和 `fixed_rate=25.0`。任务完成。

**关键指令**:
- **project_id**: 你会从启动信息中获得一个 `project_id`。在调用任何 `process_*` 工具时，**必须**正确传入这个ID。
- **忠于原文**: 构建 `column_map` 时，值必须是你在 `get_sheet_preview` 中看到的**一模一样**的列名，不要自己创造或修改。
- **一步到位**: 不要有多余的步骤。分析结束后，直接调用一个 `process_*` 工具完成任务。
"""

# --- 2. 最终的、模式驱动的工具集 ---

@tool
def get_sheet_preview(file_path: str, sheet_name: str, num_rows: int = 20) -> Tuple[str, str]:
    """
    (第一步: 分析) 读取表格预览，用于分析表格的结构、列名和计算模式。
    返回一个元组 (status, message)。
    """
    try:
        df = pd.read_excel(file_path, sheet_name=sheet_name, header=None, nrows=num_rows)
        df = df.dropna(how='all').fillna('')
        return "success", df.to_json(orient='values', index=False, force_ascii=False)
    except Exception as e:
        error_message = f"读取Excel文件 '{file_path}' 的 sheet '{sheet_name}' 预览失败: {e}"
        logger.error(error_message)
        return "error", error_message

@tool
def ask_user_for_clarification(question: str) -> Tuple[str, str]:
    """
    (信息不足时调用) 当需要人类澄清信息（例如，缺少单价）时调用此工具。
    返回一个元组 (status, message)。
    """
    # 使用interrupt函数暂停执行，等待用户输入
    user_response = interrupt(question)
    return "success", f"用户回答: {user_response}"

class ColumnMapping(BaseModel):
    """定义了从抽象概念到具体列名的映射关系"""
    employee_name: str = Field(description="员工姓名的具体列名")
    employee_id: Optional[str] = Field(None, description="员工ID的具体列名")
    quantity: Optional[str] = Field(None, description="表示'数量'的具体列名 (如 '工时', '班次')")
    price: Optional[str] = Field(None, description="表示'单价'的具体列名 (如 '时薪', '班次单价')")
    amount: Optional[str] = Field(None, description="表示最终金额的具体列名")

def _process_data_core(project_id: int, file_path: str, sheet_name: str, column_map: Dict[str, str]) -> Tuple[pd.DataFrame, Dict, DatabaseManager]:
    """
    内部核心函数：负责验证映射、定位表头、加载和清洗数据。
    """
    logger.info(f"核心处理流程启动，映射: {column_map}")
    
    # 1. 验证映射
    try:
        validated_map = ColumnMapping(**column_map)
    except ValidationError as e:
        raise ValueError(f"列映射字典验证失败: {e}")

    # 2. 定位表头和加载数据
    required_cols = [v for v in column_map.values() if v is not None]
    df_raw = pd.read_excel(file_path, sheet_name=sheet_name, header=None)
    
    header_row_index = -1
    original_to_cleaned_mapping = {}

    for i, row in df_raw.head(15).iterrows():
        # 建立原始列名到清理后列名的映射
        row_mapping = {}
        cleaned_row_values = set()

        for original_val in row.values:
            if pd.notna(original_val):
                original_str = str(original_val)
                cleaned_str = re.sub(r'\s+', ' ', original_str).strip()
                row_mapping[cleaned_str] = original_str
                cleaned_row_values.add(cleaned_str)

        # 同样清理required_cols中的列名
        cleaned_required_cols = set(re.sub(r'\s+', ' ', col).strip() for col in required_cols)

        if cleaned_required_cols.issubset(cleaned_row_values):
            header_row_index = i
            original_to_cleaned_mapping = row_mapping
            break
    
    if header_row_index == -1:
        raise FileNotFoundError(f"在表格中未能定位到包含所有关键列 '{', '.join(required_cols)}' 的表头行。")
    
    logger.success(f"成功定位到表头在第 {header_row_index + 1} 行。")
    df = pd.read_excel(file_path, sheet_name=sheet_name, header=header_row_index)

    # 3. 将传入的清理后列名映射回原始列名
    corrected_column_map = {}
    for key, cleaned_col_name in column_map.items():
        # 查找对应的原始列名
        original_col_name = None
        cleaned_target = re.sub(r'\s+', ' ', cleaned_col_name).strip()

        for col in df.columns:
            if re.sub(r'\s+', ' ', str(col)).strip() == cleaned_target:
                original_col_name = col
                break

        if original_col_name is not None:
            corrected_column_map[key] = original_col_name
        else:
            corrected_column_map[key] = cleaned_col_name  # 保持原值作为后备

    # 4. 清洗数据
    df = df.dropna(how='all').reset_index(drop=True)
    first_col_name = df.columns[0]
    summary_indicators = ["合计", "总计", "汇总", "小计"]
    for indicator in summary_indicators:
        df = df[~df[first_col_name].astype(str).str.contains(indicator, na=False)]

    logger.info(f"数据加载和清洗完毕，共 {len(df)} 行有效数据。")

    # 5. 初始化数据库连接
    db = DatabaseManager()

    # 使用修正后的列名映射
    try:
        corrected_validated_map = ColumnMapping(**corrected_column_map)
    except ValidationError as e:
        raise ValueError(f"修正后的列映射字典验证失败: {e}")

    return df, corrected_validated_map.model_dump(), db

def _save_to_db(db: DatabaseManager, project_id: int, df: pd.DataFrame, mapping: Dict[str, str], amount_series: pd.Series) -> str:
    """
    内部核心函数：负责计算和保存最终结果。
    """
    settlement_data = []
    name_col = mapping['employee_name']
    id_col = mapping.get('employee_id')

    for index, row in df.iterrows():
        try:
            name = str(row[name_col]).strip()
            if not name or name in ['nan', 'NaN']:
                continue
            
            emp_id = str(row[id_col]) if id_col and id_col in df.columns and pd.notna(row[id_col]) else None
            amount = pd.to_numeric(amount_series[index], errors='coerce')

            if pd.isna(amount):
                logger.warning(f"无法计算员工 '{name}' 的金额，该行数据可能不完整，已跳过。")
                continue
            
            settlement_data.append({
                'employee_id': emp_id,
                'employee_name': name,
                'settlement_amount': float(amount),
                'source_type': 'work_statistics'
            })
        except Exception as e:
            logger.warning(f"处理行数据时出错: {str(e)}，已跳过该行。")
            continue

    if not settlement_data:
        raise ValueError("计算完成，但未能提取任何有效的员工数据。")

    db.save_settlement_amounts(project_id, settlement_data)
    employees = [{'employee_id': item['employee_id'], 'employee_name': item['employee_name']}
                 for item in settlement_data if item.get('employee_name')]
    db.save_employees(project_id, employees)
    
    total_amount = sum(item['settlement_amount'] for item in settlement_data)
    return f"成功处理 {len(settlement_data)} 条记录，总金额: {total_amount:,.2f} 元。数据已保存。"

@tool
def process_with_multiplication(project_id: int, file_path: str, sheet_name: str, column_map: Dict[str, str]) -> Tuple[str, str]:
    """
    (模式1: 乘法) 处理所有“数量 x 单价”的场景。
    此工具将完成定位、加载、计算和存储的全过程。
    返回一个元组 (status, message)。
    """
    logger.info("启动 'process_with_multiplication' 模式...")
    try:
        df, mapping, db = _process_data_core(project_id, file_path, sheet_name, column_map)
        
        quantity_col = mapping['quantity']
        price_col = mapping['price']
        quantity = pd.to_numeric(df[quantity_col], errors='coerce')
        price = pd.to_numeric(df[price_col], errors='coerce')
        amount_series = quantity * price
        
        result_message = _save_to_db(db, project_id, df, mapping, amount_series)
        logger.success(result_message)
        return "success", result_message
    except Exception as e:
        error_message = f"处理乘法模式时出错: {e}"
        logger.error(error_message)
        return "error", error_message

@tool
def process_with_fixed_rate(project_id: int, file_path: str, sheet_name: str, column_map: Dict[str, str], fixed_rate: float) -> Tuple[str, str]:
    """
    (模式2: 固定单价) 处理“数量 x 固定单价”的场景。
    此工具将完成定位、加载、计算和存储的全过程。
    返回一个元组 (status, message)。
    """
    logger.info(f"启动 'process_with_fixed_rate' 模式，固定单价: {fixed_rate}")
    try:
        df, mapping, db = _process_data_core(project_id, file_path, sheet_name, column_map)
        
        quantity_col = mapping['quantity']
        quantity = pd.to_numeric(df[quantity_col], errors='coerce')
        amount_series = quantity * fixed_rate
        
        result_message = _save_to_db(db, project_id, df, mapping, amount_series)
        logger.success(result_message)
        return "success", result_message
    except Exception as e:
        error_message = f"处理固定单价模式时出错: {e}"
        logger.error(error_message)
        return "error", error_message

@tool
def process_from_direct_amount(project_id: int, file_path: str, sheet_name: str, column_map: Dict[str, str]) -> Tuple[str, str]:
    """
    (模式3: 直接金额) 处理直接从列中提取金额的场景。
    此工具将完成定位、加载、计算和存储的全过程。
    返回一个元组 (status, message)。
    """
    logger.info("启动 'process_from_direct_amount' 模式...")
    try:
        df, mapping, db = _process_data_core(project_id, file_path, sheet_name, column_map)
        
        amount_col = mapping['amount']
        amount_series = df[amount_col]
        
        result_message = _save_to_db(db, project_id, df, mapping, amount_series)
        logger.success(result_message)
        return "success", result_message
    except Exception as e:
        error_message = f"处理直接金额模式时出错: {e}"
        logger.error(error_message)
        return "error", error_message

# --- 3. LangGraph Agent & Workflow Integration ---

tools = [
    get_sheet_preview,
    ask_user_for_clarification,
    process_with_multiplication,
    process_with_fixed_rate,
    process_from_direct_amount,
]

def work_statistics_agent_node(state: WorkflowState):
    """
    WorkStatisticsAgent的核心节点，负责思考和调用工具。
    """
    model_name = os.getenv("MODEL_NAME", "moonshot-v1-8k")
    temperature = float(os.getenv("TEMPERATURE", 0.3))
    api_key = os.getenv("MOONSHOT_API_KEY")
    
    if not api_key:
        raise ValueError("错误: 环境变量 MOONSHOT_API_KEY 未设置。")
        
    model = MoonshotChat(model=model_name, temperature=temperature, moonshot_api_key=api_key)
    tools_for_openai = [convert_to_openai_tool(t) for t in tools]

    logger.agent_start("WorkStatisticsAgent")
    
    sheet_name = next((s_name for s_name, cls in state["sheet_classifications"].items() if cls == "用工统计表"), None)
    
    start_message_content = (
        f"请开始处理文件 `{state['file_path']}` 中的 '{sheet_name}' sheet。\n"
        f"当前项目的 project_id 是 {state['project_id']}。"
    )
    
    messages = state.get("messages", [])
    if not any(start_message_content in m.content for m in messages if isinstance(m, HumanMessage)):
         messages = [HumanMessage(content=start_message_content)] + messages

    response = model.invoke(
        [SystemMessage(content=AGENT_SYSTEM_PROMPT)] + messages,
        tools=tools_for_openai
    )
    return {'messages': [response]}

def decide_next_step(state: WorkflowState) -> str:
    """
    分析Agent的最后一条消息，决定下一步行动。
    正确处理AIMessage和ToolMessage类型。
    """
    last_message = state['messages'][-1]

    # 检查消息类型 - 只有AIMessage才有tool_calls属性
    if not isinstance(last_message, AIMessage):
        # 如果是ToolMessage（用户回答），返回到agent继续处理
        logger.info("检测到用户回答，返回Agent节点继续处理")
        return "work_statistics_agent"

    # 处理AIMessage的工具调用逻辑
    if not last_message.tool_calls:
        logger.info("Agent未调用任何工具，流程结束。")
        return END

    tool_name = last_message.tool_calls[0]['name']
    logger.info(f"Agent请求调用工具: {tool_name}")

    # 所有工具调用都统一路由到工具节点
    # 后续的复杂路由由 `route_after_work_statistics_tools` 处理
    return "call_tool"