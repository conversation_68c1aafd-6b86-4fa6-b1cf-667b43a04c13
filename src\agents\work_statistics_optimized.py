import pandas as pd
import numpy as np
from typing import Dict, List, Any, Optional, Tuple
import os
import re
import json
from langchain_community.chat_models import MoonshotChat
from langchain_core.messages import HumanMessage, SystemMessage, BaseMessage, ToolMessage
from langchain_core.messages import AIMessage
from pydantic import BaseModel, Field, ValidationError
from langchain_core.tools import tool
from langgraph.graph import StateGraph, END
from langgraph.prebuilt import ToolNode
from langchain_core.utils.function_calling import convert_to_openai_tool
from langgraph.types import interrupt

from ..state import WorkflowState
from ..database import DatabaseManager
from ..utils import logger

# --- 1. Agent System Prompt (优化版本 - 支持多种计费模式) ---
AGENT_SYSTEM_PROMPT = """你是一个专家级的薪酬计算智能体，擅长识别和处理多种计费模式。你的核心工作模式是"全面分析 -> 信息评估 -> 智能决策 -> 精准执行"。

**最终目标**: 准确无误地计算出表格中每位员工的应付薪酬，并将结果存入数据库。

**你的工具箱 (Toolbox)**:
- `get_sheet_preview`: **(分析工具)** 读取表格预览，用于分析。
- `ask_user_for_clarification`: **(分析工具)** 当信息不足时，向用户提问。
- `process_with_multiplication`: **(执行工具)** 处理所有"数量 x 单价"的场景。
- `process_with_fixed_rate`: **(执行工具)** 处理"数量 x 固定单价"的场景。
- `process_from_direct_amount`: **(执行工具)** 处理直接从表格中提取最终金额的场景。

**薪酬计算的常见模式识别**:
薪酬计算通常有以下几种模式，你必须在分析时全面考虑：

1. **工时计费模式**: 工时/小时数 × 时薪 = 薪酬
   - 关键列: 员工姓名 + 工时相关列(如"工时"、"小时数"、"总工时") + 时薪相关列(如"时薪"、"单价")

2. **班次计费模式**: 班次数/出勤天数 × 每班单价 = 薪酬  
   - 关键列: 员工姓名 + 班次相关列(如"班次"、"班次数"、"出勤天数"、"天数") + 班次单价相关列(如"班次单价"、"每班单价")

3. **天数计费模式**: 工作天数 × 日薪 = 薪酬
   - 关键列: 员工姓名 + 天数相关列(如"天数"、"工作天数"、"出勤天数") + 日薪相关列(如"日薪"、"每天单价")

4. **直接金额模式**: 表格中直接包含最终薪酬金额
   - 关键列: 员工姓名 + 金额相关列(如"薪酬"、"工资"、"金额"、"应付金额")

**核心工作流程**:

1. **全面分析 (Comprehensive Analysis)**:
   - 第一步**永远**是调用 `get_sheet_preview` 来获取表格结构
   - **系统性地检查所有可能的计费模式**，不要有偏向性
   - 识别表格中存在的列名，判断哪些模式的信息是完整的，哪些是不完整的

2. **信息充分性评估 (Information Sufficiency Assessment)**:
   - 对每种可能的计费模式，评估信息是否充分：
     * 完全充分：有员工姓名 + 数量列 + 单价列 → 可直接使用 `process_with_multiplication`
     * 部分充分：有员工姓名 + 数量列，但缺少单价 → 需要询问固定单价，然后使用 `process_with_fixed_rate`
     * 直接充分：有员工姓名 + 最终金额列 → 可直接使用 `process_from_direct_amount`
     * 信息不足：缺少关键信息 → 需要向用户询问

3. **智能询问策略 (Smart Inquiry Strategy)**:
   - 如果多种模式都可能，优先选择信息最充分的模式
   - 如果需要询问用户，要**明确指出发现的可能模式**，让用户确认使用哪种模式和提供缺失信息
   - 询问时要考虑用户回答中的关键词，如"每班"、"每小时"、"每天"等

4. **动态适应与执行 (Dynamic Adaptation & Execution)**:
   - 根据用户回答中的关键词，重新确定计费模式和对应的列名映射
   - 用户回答"每班X元" → 班次计费模式，寻找班次相关列
   - 用户回答"每小时X元" → 工时计费模式，寻找工时相关列  
   - 用户回答"每天X元" → 天数计费模式，寻找天数相关列
   - **必须根据用户回答调整 `column_map` 中的列名映射**

**场景示例**:

**示例A: 工时计费 - 信息完整**
1. 分析: `get_sheet_preview` → 发现'姓名'、'工时'、'时薪'列
2. 评估: 工时计费模式信息完整
3. 执行: `process_with_multiplication(column_map={'employee_name': '姓名', 'quantity': '工时', 'price': '时薪'})`

**示例B: 班次计费 - 需要询问单价**  
1. 分析: `get_sheet_preview` → 发现'姓名'、'班次数'列，但无单价列
2. 评估: 班次计费模式部分充分，缺少班次单价
3. 询问: `ask_user_for_clarification("表格中有班次数据，请问每班的单价是多少？")`
4. 适应: 用户回答"每班200元" → 确认班次计费模式，fixed_rate=200
5. 执行: `process_with_fixed_rate(column_map={'employee_name': '姓名', 'quantity': '班次数'}, fixed_rate=200)`

**示例C: 多模式可能 - 需要用户确认**
1. 分析: `get_sheet_preview` → 发现'姓名'、'工时'、'班次数'列，但都无对应单价
2. 评估: 工时和班次两种模式都可能，都缺少单价信息
3. 询问: `ask_user_for_clarification("表格中同时有工时和班次数据，请问按哪种方式计费？如果按工时计费，每小时多少钱？如果按班次计费，每班多少钱？")`
4. 适应: 根据用户回答确定模式和单价
5. 执行: 选择对应的工具和正确的列名映射

**关键指令**:
- **project_id**: 在调用任何 `process_*` 工具时，必须正确传入 `project_id`
- **忠于原文**: `column_map` 中的列名必须与 `get_sheet_preview` 中看到的完全一致
- **动态映射**: 根据用户回答的计费模式，选择正确的数量列名（工时列、班次列或天数列）
- **一次执行**: 分析和询问完成后，只调用一次执行工具完成任务
"""

# --- 2. 最终的、模式驱动的工具集 ---

@tool
def get_sheet_preview(file_path: str, sheet_name: str, num_rows: int = 20) -> Tuple[str, str]:
    """
    (第一步: 分析) 读取表格预览，用于分析表格的结构、列名和计算模式。
    返回一个元组 (status, message)。
    """
    try:
        df = pd.read_excel(file_path, sheet_name=sheet_name, header=None, nrows=num_rows)
        df = df.dropna(how='all').fillna('')
        return "success", df.to_json(orient='values', index=False, force_ascii=False)
    except Exception as e:
        error_message = f"读取Excel文件 '{file_path}' 的 sheet '{sheet_name}' 预览失败: {e}"
        logger.error(error_message)
        return "error", error_message

@tool
def ask_user_for_clarification(question: str) -> Tuple[str, str]:
    """
    (信息不足时调用) 当需要人类澄清信息（例如，缺少单价）时调用此工具。
    返回一个元组 (status, message)。
    """
    # 使用interrupt函数暂停执行，等待用户输入
    user_response = interrupt(question)
    return "success", f"用户回答: {user_response}"

class ColumnMapping(BaseModel):
    """定义了从抽象概念到具体列名的映射关系"""
    employee_name: str = Field(description="员工姓名的具体列名")
    employee_id: Optional[str] = Field(None, description="员工ID的具体列名")
    quantity: Optional[str] = Field(None, description="表示'数量'的具体列名 (如 '工时', '班次')")
    price: Optional[str] = Field(None, description="表示'单价'的具体列名 (如 '时薪', '班次单价')")
    amount: Optional[str] = Field(None, description="表示最终金额的具体列名")

def _process_data_core(project_id: int, file_path: str, sheet_name: str, column_map: Dict[str, str]) -> Tuple[pd.DataFrame, Dict, DatabaseManager]:
    """
    内部核心函数：负责验证映射、定位表头、加载和清洗数据。
    """
    logger.info(f"核心处理流程启动，映射: {column_map}")

    # 1. 验证映射
    try:
        validated_map = ColumnMapping(**column_map)
    except ValidationError as e:
        raise ValueError(f"列映射字典验证失败: {e}")

    # 2. 定位表头和加载数据
    required_cols = [v for v in column_map.values() if v is not None]
    df_raw = pd.read_excel(file_path, sheet_name=sheet_name, header=None)

    header_row_index = -1
    original_to_cleaned_mapping = {}

    for i, row in df_raw.head(15).iterrows():
        # 建立原始列名到清理后列名的映射
        row_mapping = {}
        cleaned_row_values = set()

        for original_val in row.values:
            if pd.notna(original_val):
                original_str = str(original_val)
                cleaned_str = re.sub(r'\s+', ' ', original_str).strip()
                row_mapping[cleaned_str] = original_str
                cleaned_row_values.add(cleaned_str)

        # 同样清理required_cols中的列名
        cleaned_required_cols = set(re.sub(r'\s+', ' ', col).strip() for col in required_cols)

        if cleaned_required_cols.issubset(cleaned_row_values):
            header_row_index = i
            original_to_cleaned_mapping = row_mapping
            break

    if header_row_index == -1:
        raise FileNotFoundError(f"在表格中未能定位到包含所有关键列 '{', '.join(required_cols)}' 的表头行。")

    logger.success(f"成功定位到表头在第 {header_row_index + 1} 行。")
    df = pd.read_excel(file_path, sheet_name=sheet_name, header=header_row_index)

    # 3. 将传入的清理后列名映射回原始列名
    corrected_column_map = {}
    for key, cleaned_col_name in column_map.items():
        # 查找对应的原始列名
        original_col_name = None
        cleaned_target = re.sub(r'\s+', ' ', cleaned_col_name).strip()

        for col in df.columns:
            if re.sub(r'\s+', ' ', str(col)).strip() == cleaned_target:
                original_col_name = col
                break

        if original_col_name is not None:
            corrected_column_map[key] = original_col_name
        else:
            corrected_column_map[key] = cleaned_col_name  # 保持原值作为后备

    # 4. 清洗数据
    df = df.dropna(how='all').reset_index(drop=True)
    first_col_name = df.columns[0]
    summary_indicators = ["合计", "总计", "汇总", "小计"]
    for indicator in summary_indicators:
        df = df[~df[first_col_name].astype(str).str.contains(indicator, na=False)]

    logger.info(f"数据加载和清洗完毕，共 {len(df)} 行有效数据。")

    # 5. 初始化数据库连接
    db = DatabaseManager()

    # 使用修正后的列名映射
    try:
        corrected_validated_map = ColumnMapping(**corrected_column_map)
    except ValidationError as e:
        raise ValueError(f"修正后的列映射字典验证失败: {e}")

    return df, corrected_validated_map.model_dump(), db

def _save_to_db(db: DatabaseManager, project_id: int, df: pd.DataFrame, mapping: Dict[str, str], amount_series: pd.Series) -> str:
    """
    内部核心函数：负责计算和保存最终结果。
    """
    settlement_data = []
    name_col = mapping['employee_name']
    id_col = mapping.get('employee_id')

    for index, row in df.iterrows():
        try:
            name = str(row[name_col]).strip()
            if not name or name in ['nan', 'NaN']:
                continue

            emp_id = str(row[id_col]) if id_col and id_col in df.columns and pd.notna(row[id_col]) else None
            amount = pd.to_numeric(amount_series[index], errors='coerce')

            if pd.isna(amount):
                logger.warning(f"无法计算员工 '{name}' 的金额，该行数据可能不完整，已跳过。")
                continue

            settlement_data.append({
                'employee_id': emp_id,
                'employee_name': name,
                'settlement_amount': float(amount),
                'source_type': 'work_statistics'
            })
        except Exception as e:
            logger.warning(f"处理行数据时出错: {str(e)}，已跳过该行。")
            continue

    if not settlement_data:
        raise ValueError("计算完成，但未能提取任何有效的员工数据。")

    db.save_settlement_amounts(project_id, settlement_data)
    employees = [{'employee_id': item['employee_id'], 'employee_name': item['employee_name']}
                 for item in settlement_data if item.get('employee_name')]
    db.save_employees(project_id, employees)

    total_amount = sum(item['settlement_amount'] for item in settlement_data)
    return f"成功处理 {len(settlement_data)} 条记录，总金额: {total_amount:,.2f} 元。数据已保存。"
